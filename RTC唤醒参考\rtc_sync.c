/**
  ******************************************************************************
  * @file    rtc_sync.c
  * @brief   GPS时间与RTC同步机制的源文件
  ******************************************************************************
  */

/* 包含头文件 */
#include "rtc_sync.h"

/* 私有变量 */
static uint8_t rtc_sync_status = RTC_SYNC_NO_GPS;
static RTC_TimeTypeDef last_sync_time = {0};
static RTC_DateTypeDef last_sync_date = {0};
static uint8_t rtc_sync_initialized = 0;

/* 私有函数声明 */
static uint8_t RTC_Sync_ValidateGPSData(GPS_Data_t *gps_data);
static void RTC_Sync_SaveLastSyncTime(void);

/**
  * @brief  初始化RTC同步机制
  * @param  无
  * @retval 无
  */
void RTC_Sync_Init(void)
{
    // 检查RTC备份寄存器，判断是否已经初始化
    if (HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR1) == 0x5AA5) {
        // 已经初始化，加载上次同步时间
        uint32_t time_data = HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR2);
        uint32_t date_data = HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR3);

        last_sync_time.Hours   = (time_data >> 16) & 0xFF;
        last_sync_time.Minutes = (time_data >> 8) & 0xFF;
        last_sync_time.Seconds = time_data & 0xFF;

        last_sync_date.Year    = (date_data >> 16) & 0xFF;
        last_sync_date.Month   = (date_data >> 8) & 0xFF;
        last_sync_date.Date    = date_data & 0xFF;

        rtc_sync_status = HAL_RTCEx_BKUPRead(&hrtc, RTC_BKP_DR4) & 0xFF;
        rtc_sync_initialized = 1;

//        printf("RTC时间已同步，上次同步时间: %02d:%02d:%02d %02d/%02d/20%02d\r\n",
//               last_sync_time.Hours, last_sync_time.Minutes, last_sync_time.Seconds,
//               last_sync_date.Date, last_sync_date.Month, last_sync_date.Year);
    } else {
        // 首次初始化
        memset(&last_sync_time, 0, sizeof(RTC_TimeTypeDef));
        memset(&last_sync_date, 0, sizeof(RTC_DateTypeDef));
        rtc_sync_status = RTC_SYNC_NO_GPS;
        rtc_sync_initialized = 1;

        // 标记已初始化
        HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR1, 0x5AA5);
        HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR2, 0);
        HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR3, 0);
        HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR4, rtc_sync_status) ;

//        printf("RTC同步机制首次初始化\r\n") ;
    }
}

/**
  * @brief  检查RTC是否需要同步
  * @param  无
  * @retval 1: 需要同步, 0: 不需要同步
  */
uint8_t RTC_Sync_IsNeeded(void)
{
    // 如果未初始化，先初始化
    if (!rtc_sync_initialized) {
        RTC_Sync_Init();
    }

    // 获取当前RTC时间和日期
    RTC_TimeTypeDef current_time;
    RTC_DateTypeDef current_date;
    RTC_GetDateTime(&current_time, &current_date);

    // 如果从未同步过，需要同步
    if (last_sync_date.Year == 0 && last_sync_date.Month == 0 && last_sync_date.Date == 0) {
        return 1;
    }

    // 如果当前年份小于2020，需要同步
    if (current_date.Year + 2000 < 2020) {
        return 1;
    }

    // 如果上次同步状态不是成功，需要同步
    if (rtc_sync_status != RTC_SYNC_SUCCESS) {
        return 1;
    }

    // 计算上次同步到现在的天数
    // 简化计算，仅考虑年月日的差值
    uint32_t days_since_sync = 0;

    // 如果年份不同
    if (current_date.Year != last_sync_date.Year) {
        // 每年按365天计算
        days_since_sync += (current_date.Year - last_sync_date.Year) * 365;
    }

    // 如果月份不同
    if (current_date.Month != last_sync_date.Month) {
        // 每月按30天计算
        days_since_sync += (current_date.Month - last_sync_date.Month) * 30;
    }

    // 日期差值
    days_since_sync += (current_date.Date - last_sync_date.Date);

    // 如果超过7天未同步，需要同步
    if (days_since_sync >= 7) {
        return 1;
    }

    return 0;
}

/**
  * @brief  尝试使用GPS数据同步RTC
  * @param  无
  * @retval 同步状态码
  */
uint8_t RTC_Sync_TrySync(void)
{
    // 如果未初始化，先初始化
    if (!rtc_sync_initialized) {
        RTC_Sync_Init();
    }

    // 检查GPS数据是否就绪
    if (!GPS_IsDataReady()) {
        rtc_sync_status = RTC_SYNC_NO_GPS;
        return rtc_sync_status;
    }

    // 验证GPS数据
    uint8_t validation_result = RTC_Sync_ValidateGPSData(&gps_data);
    if (validation_result != RTC_SYNC_SUCCESS) {
        rtc_sync_status = validation_result;
        return rtc_sync_status;
    }

    // 同步RTC时钟
    if (GPS_SyncRTC(&gps_data)) {
        rtc_sync_status = RTC_SYNC_SUCCESS;
        // 保存同步时间
        RTC_Sync_SaveLastSyncTime();
//        printf("RTC时钟同步成功!\r\n");
    } else {
        rtc_sync_status = RTC_SYNC_HAL_ERROR;
//        printf("RTC时钟同步失败:HAL错误\r\n");
    }

    // 保存同步状态
    HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR4, rtc_sync_status);

    return rtc_sync_status;
}

/**
  * @brief  等待GPS数据并同步RTC
  * @param  timeout_ms: 超时时间(ms)
  * @retval 同步状态码
  */
uint8_t RTC_Sync_WaitAndSync(uint32_t timeout_ms)
{
    uint32_t start_time = HAL_GetTick();
    uint8_t attempts = 0;

    // 如果未初始化，先初始化
    if (!rtc_sync_initialized) {
        RTC_Sync_Init();
    }

//    printf("等待GPS数据并同步RTC.....\r\n");

    // 循环尝试同步，直到成功或超时
    while (1) {
        // 尝试同步
        uint8_t sync_result = RTC_Sync_TrySync();

        // 如果同步成功，返回
        if (sync_result == RTC_SYNC_SUCCESS) {
            return sync_result;
        }

        // 增加尝试次数
        attempts++;

        // 如果超过最大尝试次数，返回
        if (attempts >= RTC_SYNC_MAX_ATTEMPTS) {
//            printf("超过最大尝试次数，同步失败!\r\n");
            rtc_sync_status = RTC_SYNC_TIMEOUT;
            return rtc_sync_status;
        }

        // 如果超时，返回
        if (HAL_GetTick() - start_time > timeout_ms) {
//            printf("同步超时\r\n");
            rtc_sync_status = RTC_SYNC_TIMEOUT;
            return rtc_sync_status;
        }

        // 延时一段时间再尝试
        HAL_Delay(RTC_SYNC_ATTEMPT_DELAY);
//				printf(" %d ci" , attempts + 1);
    }
}

/**
  * @brief  获取上次同步时间
  * @param  time: RTC时间结构体指针
  * @param  date: RTC日期结构体指针
  * @retval 无
  */
void RTC_Sync_GetLastSyncTime(RTC_TimeTypeDef *time, RTC_DateTypeDef *date)
{
    // 如果未初始化，先初始化
    if (!rtc_sync_initialized) {
        RTC_Sync_Init();
    }

    if (time != NULL) {
        memcpy(time, &last_sync_time, sizeof(RTC_TimeTypeDef));
    }

    if (date != NULL) {
        memcpy(date, &last_sync_date, sizeof(RTC_DateTypeDef));
    }
}

/**
  * @brief  获取同步状态描述
  * @param  status: 同步状态码
  * @retval 状态描述字符串
  */
const char* RTC_Sync_GetStatusString(uint8_t status)
{
    switch (status) {
        case RTC_SYNC_SUCCESS:
            return "同步成功";
        case RTC_SYNC_NO_GPS:
            return "无GPS数据";
        case RTC_SYNC_INVALID_DATA:
            return "GPS数据无效";
        case RTC_SYNC_INVALID_DATE:
            return "日期无效";
        case RTC_SYNC_HAL_ERROR:
            return "HAL错误";
        case RTC_SYNC_TIMEOUT:
            return "同步超时";
        default:
            return "weizhizhuangtai" ;
    }
}

/**
  * @brief  获取当前RTC同步状态
  * @param  无
  * @retval 当前同步状态码
  */
uint8_t RTC_Sync_GetCurrentStatus(void)
{
    // 如果未初始化，先初始化
    if (!rtc_sync_initialized) {
        RTC_Sync_Init();
    }

    return rtc_sync_status;
}

/**
  * @brief  验证GPS数据
  * @param  gps_data: GPS数据结构体指针
  * @retval 验证结果
  */
static uint8_t RTC_Sync_ValidateGPSData(GPS_Data_t *gps_data)
{
    // 检查GPS数据是否有效
    if (!gps_data->valid) {
        return RTC_SYNC_INVALID_DATA;
    }

    // 检查日期是否有效
    if (gps_data->year < 2020 || gps_data->month < 1 || gps_data->month > 12 ||
        gps_data->day < 1 || gps_data->day > 31) {
        return RTC_SYNC_INVALID_DATE;
    }

    return RTC_SYNC_SUCCESS;
}

/**
  * @brief  保存上次同步时间
  * @param  无
  * @retval 无
  */
static void RTC_Sync_SaveLastSyncTime(void)
{
    // 获取当前RTC时间和日期
    RTC_GetDateTime(&last_sync_time, &last_sync_date);

    // 保存到备份寄存器
    uint32_t time_data = ((uint32_t)last_sync_time.Hours << 16) |
                         ((uint32_t)last_sync_time.Minutes << 8) |
                         last_sync_time.Seconds;

    uint32_t date_data = ((uint32_t)last_sync_date.Year << 16) |
                         ((uint32_t)last_sync_date.Month << 8) |
                         last_sync_date.Date;

    HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR2, time_data);
    HAL_RTCEx_BKUPWrite(&hrtc, RTC_BKP_DR3, date_data);
}
