# STM32L071 低功耗物联网主设备项目

## 项目概述

本项目是基于STM32L071KBU6单片机的低功耗物联网主设备，集成多种通信方式和传感器，实现远程数据采集和传输功能。

### 硬件组成
- **主控**: STM32L071KBU6 (低功耗ARM Cortex-M0+)
- **近场通信**: E70 LoRa模块 (与子设备通信)
- **远程通信**: 4G CAT1模块 AIR780EG (与服务器通信，集成GPS)
- **电源管理**: 电池电压检测电路
- **唤醒触发**: 2个光敏传感器 + 1个触摸传感器
- **存储**: 内置Flash (数据缓存)
- **时钟**: RTC (时间同步)

### 主要功能
1. **低功耗休眠唤醒机制** - 外部触发唤醒，STOP模式休眠
2. **E70 LoRa通信** - 获取子设备数据
3. **GPS定位功能** - 通过CAT1模块获取位置信息
4. **4G网络通信** - 数据上传和指令接收
5. **电池电压监测** - 实时监控设备电源状态
6. **历史数据缓存** - Flash存储失败数据，支持重传
7. **RTC时间同步** - GPS UTC时间同步
8. **智能数据重传** - 发送失败自动缓存，成功后发送历史数据

## 开发状态总览

### ✅ 已完成功能
1. **基础系统**
   - STM32L071 HAL库初始化
   - GPIO、UART、ADC、RTC外设配置
   - 低功耗STOP模式休眠唤醒机制

2. **E70 LoRa通信模块**
   - E70模块初始化和配置
   - 主从设备通信协议
   - 子设备数据接收和解析
   - 电源管理和模式切换

3. **CAT1模块基础功能**
   - AIR780EG模块初始化
   - AT指令通信框架
   - GPS功能开启和数据查询
   - 基本的网络连接功能

4. **GPS定位功能**
   - GPS数据查询和解析
   - 位置有效性检查
   - 详细GPS信息显示
   - 信号质量评估

5. **电池电压监测**
   - ADC采样和电压计算
   - 电压校准机制

6. **外部触发唤醒**
   - 光敏传感器中断配置
   - 触摸传感器中断配置
   - 中断安全处理

7. **Flash历史数据管理**
   - 环形缓冲区存储（200条记录）
   - 发送失败自动保存为B格式
   - 智能重传机制
   - 栈溢出优化（全局缓冲区）

8. **测试和调试**
   - 通信测试模式
   - 状态机调试输出
   - GPS数据连续监控
   - Flash读写测试（支持500条记录测试）

### ✅ 最新完成功能
1. **历史数据缓存系统** - 完整的Flash存储和重传机制
2. **栈溢出优化** - 所有大缓冲区移至全局变量
3. **智能数据重传** - 实时数据优先，历史数据补发
4. **内存安全优化** - 避免局部大数组导致的系统崩溃
5. **ZL指令系统** - 完整的休眠时间动态设置功能
6. **双唤醒机制** - RTC定时唤醒 + GPIO外部触发唤醒

### 🚧 待开发功能
1. **完整业务流程优化** - 进一步的功耗和稳定性优化

## 硬件连接说明

### E70 LoRa模块
| 功能 | STM32引脚 | 说明 |
|------|-----------|------|
| M0模式控制 | PA4 | 工作模式设置 |
| M1模式控制 | PA5 | 工作模式设置 |
| M2模式控制 | PA6 | 工作模式设置 |
| 电源控制 | RF_PWR_Pin | 模块电源开关 |
| 串口通信 | LPUART1 | 数据传输 |

#### E70工作模式
```
M2 M1 M0 | 模式说明
---------|----------
0  <USER>  <GROUP>  | 连续通信模式 (正常工作) ✅
1  0  1  | 配置模式 (参数设置) ✅
其他     | 不使用
```

### CAT1模块 (AIR780EG)
| 功能 | STM32引脚 | 说明 |
|------|-----------|------|
| 串口通信 | USART1 | AT指令和数据传输 |
| 电源控制 | CAT1_PWR_Pin | 模块电源开关 |

### 传感器和外设
| 功能 | STM32引脚 | 说明 |
|------|-----------|------|
| 光敏传感器1 | PB5 (INT1) | 主唤醒触发，上升沿中断 |
| 触摸传感器 | PA8 (INT2) | 辅助唤醒触发，上升沿中断 |
| 环境检测开关 | PB4 (EN_Pin) | 高电平=禁止工作，低电平=允许工作 |
| 电池电压检测 | PB0 (ADC) | 2:1分压采样 |
| 状态指示LED | PC14 | 工作状态指示 |
| 调试串口 | USART4 | 115200bps调试输出 |


## 完整业务流程设计

### 设备初次上电流程
```mermaid
graph TD
    A[设备上电] --> B[初始化各外设]
    B --> C[获取电池电压]
    C --> D[开启CAT1模块]
    D --> E[初始化CAT1模块]
    E --> F[开启GPS功能]
    F --> G[获取GPS数据<br/>超时2分钟]
    G --> H{GPS定位成功?}
    H -->|是| I[提取GPS信息暂存]
    H -->|否| J[使用默认位置信息]
    I --> K[同步更新RTC]
    J --> K
    K --> L[关闭GPS功能]
    L --> M[获取信号值和CCID]
    M --> N[数据打包HY协议]
    N --> O[连接网络服务器]
    O --> P[发送数据到服务器]
    P --> Q{收到ZL指令?}
    Q -->|是| R[更新Flash配置]
    Q -->|否| S[数据转B格式存Flash]
    R --> T[关闭服务器连接]
    S --> T
    T --> U[进入休眠等待唤醒]
```

### 设备唤醒后流程
```mermaid
graph TD
    A[外部触发唤醒] --> B[初始化各外设]
    B --> C[开启E70模块]
    C --> D[获取子设备数据]
    D --> E[关闭E70模块]
    E --> F[获取电池电压]
    F --> G[开启CAT1模块]
    G --> H[初始化CAT1模块]
    H --> I[开启GPS功能]
    I --> J[获取GPS数据<br/>超时2分钟]
    J --> K{GPS定位成功?}
    K -->|是| L[提取GPS信息暂存]
    K -->|否| M[使用默认位置信息]
    L --> N[同步更新RTC]
    M --> N
    N --> O[关闭GPS功能]
    O --> P[获取信号值和CCID]
    P --> Q[数据打包HY协议]
    Q --> R[连接网络服务器]
    R --> S[发送实时数据]
    S --> T{实时数据发送成功?}
    T -->|是| U[检查历史数据]
    T -->|否| V[保存为B格式到Flash]
    U --> W{有历史数据?}
    W -->|是| X[发送历史B数据<br/>最多10条]
    W -->|否| Y[关闭服务器连接]
    V --> Y
    X --> Z{历史数据发送成功?}
    Z -->|是| AA[继续发送剩余历史数据]
    Z -->|否| BB[重新保存失败数据]
    AA --> Y
    BB --> Y
    Y --> CC[进入休眠等待唤醒]
```

## 数据协议规范

### HY数据包格式
```
HY[长度][标识]+[经度]+[纬度]+[UTC时间]+[海拔]+[GPS状态]+[卫星数]+[速度]+[电池电压]+[HDOP]+[PDOP]+[方位角]+[子设备ID]+[子设备电压]+[GSM信号]+[ICCID]+E
```
HY106S+119.962120+30.276014+20250906033008+48.0+1+13+0.23+3.86+4.26+1.75+0.0+1001+4.8+19+89852312278524093435+E

#### 字段说明
- **标识**: S=实时数据, B=缓存数据
- **经度/纬度**: 度分格式，精度6位小数
- **UTC时间**: YYYYMMDDHHMMSS格式
- **GPS状态**: 0=无效, 1=有效
- **子设备ID**: 默认0000 (初次上电)
- **子设备电压**: 默认0.0 (初次上电)

### 网络指令协议

#### ZL指令系统
- **ZL+F16E22**: 工作时间段设置 (16:00-22:00) - 保存到Flash
- **ZL+H12**: 休眠小时数设置 (1-8760小时，超过18小时限制为18小时) - 内存变量
- **ZL+M30**: 休眠分钟数设置 (1-525600分钟) - 内存变量
- **ZL+S600**: 休眠秒数设置 (1-31536000秒) - 内存变量
- **ZL+N10**: 存储N条数据后发送 (0=禁用，>5启用) - 保存到Flash
- **ZL+A5**: 速度阈值设置 (1-200节) - 保存到Flash
- **ZL+B300**: 快发模式休眠时间 (1-86400秒) - 保存到Flash

#### 重要说明
- **S/M/H指令**: 直接更新全局变量`user_sleep_seconds`，不保存到Flash，设备断电后恢复默认30秒
- **其他指令**: 保存到Flash，设备重启后自动加载
- **18小时限制**: 由于RTC硬件限制，最大休眠时间为18小时（64800秒）

#### 智能重传机制
1. **实时数据优先**: 每次连接先发送当前实时数据
2. **失败自动缓存**: 发送失败的S数据自动转为B格式存入Flash
3. **历史数据补发**: 实时数据成功后，自动发送历史B数据
4. **环形缓冲管理**: 最多存储200条历史数据，满后覆盖最旧数据
5. **批量发送控制**: 每次最多发送10条历史数据，避免超时
6. **失败重新缓存**: 历史数据发送失败会重新保存到Flash

## Flash历史数据管理系统

### 存储架构
```
STM32L071 Flash Layout (128KB):
0x08000000 - 0x08017FFF: 程序代码 (96KB)
0x08018000 - 0x0801FEFF: 历史数据存储 (200条×128字节=25.6KB)
0x0801FF00 - 0x0801FFFF: 管理信息存储 (256字节)
```

### 核心功能
1. **环形缓冲区**: 最大200条记录，满后自动覆盖最旧数据
2. **数据格式转换**: 发送失败的S数据自动转为B格式存储
3. **智能重传**: 实时数据成功后自动发送历史数据
4. **批量控制**: 每次最多发送10条历史数据
5. **故障恢复**: 历史数据发送失败会重新保存

### 关键函数接口
```c
// 初始化历史数据存储
void Flash_InitHistoryStorage(void);

// 保存历史数据（S→B转换）
HAL_StatusTypeDef Flash_SaveHistoryData(const char* data);

// 获取下一条历史数据
HAL_StatusTypeDef Flash_GetNextHistoryData(char* data, uint16_t max_len);

// 获取历史数据条数
uint16_t Flash_GetHistoryDataCount(void);

// 清除所有历史数据
void Flash_ClearAllHistoryData(void);
```

### 内存安全优化
- **全局缓冲区**: 使用`history_backup_buffer[128]`避免栈溢出
- **缓冲区复用**: 历史数据读取复用`global_data_packet[256]`
- **栈使用监控**: 所有函数栈使用控制在20字节以内

## 代码架构说明

### 主要文件结构
```
Core/
├── Src/
│   ├── main.c              # 主程序和状态机
│   ├── GSM.c              # CAT1模块驱动
│   ├── GPS.c              # GPS功能封装
│   ├── e70_config.c       # E70模块配置
│   ├── network_command.c  # 网络指令解析
│   └── globals.c          # 全局变量定义
├── Inc/
│   ├── GSM.h              # CAT1模块接口
│   ├── GPS.h              # GPS功能接口
│   ├── e70_config.h       # E70配置参数
│   ├── network_command.h  # 指令解析接口
│   └── globals.h          # 全局变量声明
└── FLASH/
    ├── bsp_flash.c        # Flash存储驱动
    └── bsp_flash.h        # Flash接口定义
```

### 状态机设计
```c
typedef enum {
    MASTER_STATE_WAKE_UP,           // 唤醒初始化
    MASTER_STATE_E70_WAKE_SLAVE,    // E70唤醒子设备
    MASTER_STATE_WAIT_SLAVE_DATA,   // 等待子设备数据
    MASTER_STATE_SEND_ACK,          // 发送确认
    MASTER_STATE_VOLTAGE_CHECK,     // 电压检测
    MASTER_STATE_CAT1_GPS,          // GPS数据获取 (待开发)
    MASTER_STATE_CAT1_UPLOAD,       // 数据上传 (待开发)
    MASTER_STATE_PREPARE_SLEEP      // 准备休眠
} MasterState_t;
```

## 开发指南

### 环境配置
- **IDE**: Keil MDK-ARM
- **调试**: ST-Link + UART4 (115200bps)
- **编程**: 不要尝试编译，直接烧录hex文件

### 测试模式
在main.c中设置测试模式标志：
```c
static uint8_t communication_test_mode = 1;  // 1=测试模式, 0=正常模式
```

### 关键配置参数

#### 设备标识配置
```c
// e70_config.h
#define MCU_DEVICE_ID           0x1002      // 单片机设备ID，生产时每个设备不同
#define E70_MODULE_ADDRESS      0x1234      // E70模块地址，ID相同才能组网通信

// E70通信数据包格式
typedef struct {
    uint8_t header1;        // 0xFA
    uint8_t header2;        // 0xFB
    uint16_t mcu_device_id; // 单片机设备ID (高位在前)
    uint16_t battery_voltage; // 电池电压
    uint8_t end;            // 0xFD
} E70_SendPacket_t;

typedef struct {
    uint8_t header1;        // 0xFC
    uint8_t header2;        // 0xFD
    uint8_t ack1;           // 0x55
    uint8_t ack2;           // 0x66
    uint8_t end;            // 0xFD
} E70_RecvPacket_t;
```

#### 服务器配置
```c
// GSM.h
#define GSM_SERVER_IP               "************"    // 测试服务器IP地址
#define GSM_SERVER_PORT             48086             // 端口号
//#define GSM_SERVER_IP               "**********"    // 公司服务器IP地址
//#define GSM_SERVER_PORT             58085             // 端口号
```

#### 电池校准配置
```c
// e70_config.h
#define BATTERY_VOLTAGE_CALIBRATION_FACTOR  1.0f     // 电池电压校准系数
#define ADC_VREFINT_CAL_VREF                3000     // 校准时的参考电压(mV)
```

#### 休眠时间配置
```c
// main.c
#define RTC_WAKEUP_INTERVAL_SECONDS  30              // RTC唤醒间隔：默认30秒
#define ENABLE_RTC_WAKEUP  1                         // 1=启用RTC定时唤醒，0=仅外部触发

// 全局变量
extern uint32_t user_sleep_seconds;                 // 动态休眠时间，默认30秒
```

#### E70工作模式配置
```c
// e70_config.h
typedef enum {
    E70_MODE_TRANS = 1,         // 连续通信模式 (001) - 正常通信模式 ✅
    E70_MODE_CONFIG5 = 5,       // 配置模式5 (101) - 实测有效的配置模式 ✅
} E70_Mode_t;

// 重要：必须使用配置模式5 (101) 进行配置，模式3 (011) 无响应
// 必须在上电前设置模式引脚，不能运行时切换
```

### 调试技巧
1. **串口监控**: 使用UART4观察调试输出
2. **状态跟踪**: 观察状态机转换日志
3. **电流测量**: 监控休眠和工作电流
4. **GPS测试**: 使用测试模式验证GPS功能

### 状态机详细说明

#### 唤醒源识别
```c
typedef enum {
    WAKEUP_SOURCE_UNKNOWN = 0,
    WAKEUP_SOURCE_GPIO,     // 外部GPIO中断唤醒 (INT1/INT2)
    WAKEUP_SOURCE_RTC       // RTC定时器唤醒
} WakeupSource_t;
```

#### 主状态机流程
```c
typedef enum {
    MASTER_STATE_WAKE_UP = 0,           // 唤醒阶段 - 根据唤醒源决定流程
    MASTER_STATE_E70_WAKE_SLAVE,        // 唤醒子设备阶段 - 仅GPIO唤醒执行
    MASTER_STATE_WAIT_SLAVE_DATA,       // 等待子设备数据阶段
    MASTER_STATE_SEND_ACK,              // 发送确认阶段
    MASTER_STATE_VOLTAGE_CHECK,         // 电压检测阶段
    MASTER_STATE_CAT1_GPS,              // CAT1 GPS数据获取阶段
    MASTER_STATE_HELLO_TEST,            // 简单测试阶段
    MASTER_STATE_CAT1_UPLOAD,           // CAT1上传数据阶段
    MASTER_STATE_PREPARE_SLEEP          // 准备休眠阶段
} MasterState_t;
```

#### 关键业务逻辑
- **RTC唤醒**: 跳过E70通信，直接进入电压检测，使用默认子设备数据(ID=0000, 电压=0.0)
- **GPIO唤醒**: 执行完整E70通信流程，获取真实子设备数据
- **GPS超时**: 2分钟(120秒)，超时后使用默认坐标(119.962+30.276)
- **历史数据**: 实时数据成功后自动发送，每次最多10条，失败重新保存



## 注意事项

### 硬件相关
- **E70模式**: 必须在上电前设置，运行时不能切换，只能使用模式5(101)配置，模式3(011)无响应
- **CAT1模块**: 启动需要3-5秒稳定时间，使用AT+CGNSPWR=1开启GPS，AT+CGNSINF查询GPS数据
- **GPS定位**: 首次定位1-2分钟，超时120秒后使用默认坐标
- **INT中断**: INT1/INT2配置为上升沿触发，内部下拉电阻，容易误触发需硬件改进

### 软件相关
- **中断安全**: 中断回调中避免使用printf和长时间操作，不要在RTC中断中使用printf
- **状态机**: 切换要确保资源正确释放，LED_ON表示唤醒，LED_OFF表示休眠
- **Flash操作**: 注意数据完整性，环形缓冲区200条记录，满后覆盖最旧数据
- **栈溢出预防**: 避免在函数中定义大于64字节的局部数组，使用全局缓冲区
- **内存管理**: 大缓冲区使用全局变量，如global_data_packet[256]、history_backup_buffer[128]
- **缓冲区复用**: 合理复用全局缓冲区，避免内存浪费

### 调试相关
- **串口输出**: UART4调试输出115200bps，不影响USART1(CAT1)和LPUART1(E70)功能串口
- **测试模式**: 设置communication_test_mode=1启用GPS连续测试，=0正常状态机模式
- **电流测量**: 休眠<10μA，工作<200mA，注意模块启动瞬间电流
- **ZL指令测试**: 服务器发送ZL+S60设置60秒休眠，ZL+M5设置5分钟休眠

### 关键全局变量
```c
// 休眠时间控制
extern uint32_t user_sleep_seconds;                 // 动态休眠时间，默认30秒

// 唤醒源和状态
static WakeupSource_t current_wakeup_source;        // 当前唤醒源
static volatile uint8_t rtc_wakeup_flag;            // RTC唤醒标志
static uint8_t interrupt_enabled;                   // 中断使能标志

// 数据缓冲区
char global_data_packet[256];                       // 全局数据包缓冲区
char history_backup_buffer[128];                    // 历史数据备份缓冲区
char global_gsm_response_buffer[512];               // GSM响应缓冲区

// 设备数据
static uint16_t saved_battery_voltage;              // 保存的电池电压值
static uint16_t slave_device_id;                    // 子设备ID
static uint16_t slave_battery_voltage;              // 子设备电池电压
```

## 性能指标

### Flash存储性能
- **存储容量**: 200条历史记录（25.6KB）
- **写入速度**: 单条记录约50ms
- **读取速度**: 单条记录约5ms
- **数据完整性**: 100%（经500条记录测试验证）
- **擦除寿命**: 10,000次（Flash规格）

### 内存使用优化
- **栈使用**: 优化前>1KB → 优化后<50字节
- **全局变量**: 约2KB（包含所有缓冲区）
- **代码空间**: 约85KB（剩余43KB可用）
- **系统稳定性**: 无HardFault，连续运行稳定

### 通信性能
- **数据上传成功率**: >95%（网络正常情况下）
- **历史数据重传**: 自动补发，无数据丢失
- **GPS定位时间**: 首次1-2分钟，后续30-60秒
- **整体功耗**: 休眠<10μA，工作<200mA

## 版本历史

### v2.2.0 (当前版本)
- ✅ 完整的Flash历史数据缓存系统
- ✅ 栈溢出问题彻底解决
- ✅ 智能数据重传机制
- ✅ 内存安全优化
- ✅ 500条记录Flash测试通过
- ✅ ZL指令休眠时间动态设置(S/M/H指令)
- ✅ 双唤醒机制(RTC定时+GPIO外部触发)
- ✅ GPS超时修复(120秒)和默认坐标机制

### v2.1.0
- ✅ 完整的Flash历史数据缓存系统
- ✅ 栈溢出问题彻底解决
- ✅ 智能数据重传机制
- ✅ 内存安全优化
- ✅ 500条记录Flash测试通过

### v2.0.0
- ✅ 完整的GPS+CAT1+E70通信流程
- ✅ HY协议数据包生成
- ✅ 网络服务器连接和数据传输
- ✅ RTC时间同步

### v1.0.0
- ✅ 基础硬件驱动和状态机
- ✅ E70 LoRa通信
- ✅ 低功耗休眠唤醒

