/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32l0xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LED_Pin GPIO_PIN_14
#define LED_GPIO_Port GPIOC
#define M0_Pin GPIO_PIN_4
#define M0_GPIO_Port GPIOA
#define M1_Pin GPIO_PIN_5
#define M1_GPIO_Port GPIOA
#define M2_Pin GPIO_PIN_6
#define M2_GPIO_Port GPIOA
#define V_OUT_Pin GPIO_PIN_1
#define V_OUT_GPIO_Port GPIOB
#define RF_PWR_Pin GPIO_PIN_11
#define RF_PWR_GPIO_Port GPIOA
#define CAT1_PWR_Pin GPIO_PIN_12
#define CAT1_PWR_GPIO_Port GPIOA
#define EN_Pin GPIO_PIN_4
#define EN_GPIO_Port GPIOB
#define INT1_Pin GPIO_PIN_5
#define INT1_GPIO_Port GPIOB
#define INT1_EXTI_IRQn EXTI4_15_IRQn

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
