/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "usart.h"
#include "rtc.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include "GSM.h"
#include "GPS.h"
#include <string.h>
#include <stdio.h>
#include "network_command.h"
#include "globals.h"
#include "FLASH/bsp_flash.h"
// #include "simple_cache.h"  // 缓存功能已删除
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// 母设备业务流程控制变量
static uint8_t e70_first_init_done = 0;        // E70首次初始化标志
static uint32_t e70_wake_start_time = 0;       // E70唤醒开始时间
static uint8_t e70_wake_retry_count = 0;       // E70唤醒重试次数
static uint16_t saved_battery_voltage = 0;     // 保存的电池电压值
static uint16_t slave_device_id = 0;           // 子设备ID
static uint16_t slave_battery_voltage = 0;     // 子设备电池电压
static uint8_t interrupt_enabled = 1;          // 中断使能标志，1=允许响应，0=禁止响应
uint8_t		INT_TEST;


// 通信测试模式控制变量
// 修改这个值来切换模式：1=测试模式，0=正常状态机模式
static uint8_t communication_test_mode = 0;    // 测试模式标志，1=测试模式，0=正常状态机模式
//static uint32_t last_send_time = 0;            // 上次发送时间

// RTC定时唤醒配置
#define ENABLE_RTC_WAKEUP  1                    // 1=启用RTC定时唤醒，0=仅外部触发
#define RTC_WAKEUP_INTERVAL_SECONDS  30         // RTC唤醒间隔：3600秒(1小时)

// 唤醒源识别
typedef enum {
    WAKEUP_SOURCE_UNKNOWN = 0,
    WAKEUP_SOURCE_GPIO,     // 外部GPIO中断唤醒
    WAKEUP_SOURCE_RTC       // RTC定时器唤醒
} WakeupSource_t;

static WakeupSource_t current_wakeup_source = WAKEUP_SOURCE_UNKNOWN;
static volatile uint8_t rtc_wakeup_flag = 0;  // RTC唤醒标志

// 母设备业务流程状态枚举
typedef enum {
    MASTER_STATE_WAKE_UP = 0,           // 唤醒阶段
    MASTER_STATE_E70_WAKE_SLAVE,        // 唤醒子设备阶段
    MASTER_STATE_WAIT_SLAVE_DATA,       // 等待子设备数据阶段
    MASTER_STATE_SEND_ACK,              // 发送确认阶段
    MASTER_STATE_VOLTAGE_CHECK,         // 电压检测阶段（E70完成后）
    MASTER_STATE_CAT1_GPS,              // CAT1 GPS数据获取阶段（预留）
    MASTER_STATE_HELLO_TEST,            // 简单测试阶段
    MASTER_STATE_CAT1_UPLOAD,           // CAT1上传数据阶段（预留）
    MASTER_STATE_PREPARE_SLEEP          // 准备休眠阶段
} MasterState_t;

static MasterState_t current_state = MASTER_STATE_WAKE_UP;

// CAT1模块信息全局变量
char global_ccid[32] = {0};
int8_t global_signal_dbm = -128;

// 历史数据发送状态
typedef enum {
    HISTORY_STATE_IDLE = 0,           // 空闲状态
    HISTORY_STATE_SENDING,            // 正在发送历史数据
    HISTORY_STATE_COMPLETE            // 历史数据发送完成
} HistoryState_t;

//static HistoryState_t history_send_state = HISTORY_STATE_IDLE;

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
// Flash测试函数在bsp_flash.h中声明

// 历史数据管理函数
void PrintHistoryDataStatus(void);
void TestHistoryDataRead(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
uint8_t txmp[6]={0xC0,0x00,0x00,0x18,0x04,0x1C};
uint8_t rxmp[10];
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_RTC_Init();
  MX_LPUART1_UART_Init();
  MX_USART4_UART_Init();
  /* USER CODE BEGIN 2 */
  // printf("=== STM32L071 Master Device Low Power System ===\r\n");

  // 打印设备启动时的RTC时间
  RTC_PrintCurrentTime("Device startup time: ");

  // 历史数据系统完整测试（测试真实的保存和读取函数）
//  printf("=== History Data System Test ===\r\n");
//  Flash_TestHistoryDataSystem(20);  // 测试5条记录，取消注释即可启用测试

  // 初始化历史数据存储
  printf("=== History Data Storage Init ===\r\n");
  Flash_InitHistoryStorage();
  printf("History data count: %d\r\n", Flash_GetHistoryDataCount());

  // 测试历史数据功能
//  printf("=== History Data Test ===\r\n");
//  // 保存几条测试数据
//  Flash_SaveHistoryData("HY105S+119.962044+30.276072+20250908091815+23.6+1+11+0.05+3.87+2.46+1.33+0.0+1001+4.8+27+898523122785240001+E");
//  Flash_SaveHistoryData("HY105S+119.962044+30.276072+20250908091815+23.6+1+11+0.05+3.87+2.46+1.33+0.0+1001+4.8+27+898523122785240002+E");
//  printf("Test data saved. History count: %d\r\n", Flash_GetHistoryDataCount());

  // 显示历史数据状态
  PrintHistoryDataStatus();

  // 初始化休眠时间全局变量（默认30秒）
  extern uint32_t user_sleep_seconds;
  if (user_sleep_seconds == 0) {
      user_sleep_seconds = 30;  // 默认30秒
//      printf("Sleep time initialized to default: %lu seconds\r\n", user_sleep_seconds);
  }

  LED_ON;

  // E70首次初始化（生命周期内只执行一次）
  if (!e70_first_init_done) {
//    printf("=== E70 First Time Initialization ===\r\n");
    HAL_GPIO_WritePin(GPIOA, M0_Pin|M1_Pin|M2_Pin|RF_PWR_Pin|CAT1_PWR_Pin, GPIO_PIN_RESET);

    // 使用简化配置函数（只发送配置，不验证）
    E70_InitializeConfigSimple(E70_MODULE_ADDRESS, 10, 1);
    E70_EnterCommMode();

//    printf("=== E70 Configuration Complete ===\r\n");
    e70_first_init_done = 1;  // 标记首次初始化完成
  }

  // 初始化时关闭所有模块电源
  RF_PWR_OFF;   // 关闭E70模块
  CAT1_PWR_OFF; // 关闭CAT1模块

  // 启用GPIO中断 - INT1和INT2用于唤醒
  HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);

  // 启用UART中断 - 确保E70通信正常
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);

//  printf("Master Device Ready\r\n");

  // 显示当前工作模式
//  if (communication_test_mode) {
//    printf("=== COMMUNICATION TEST MODE ===\r\n");
//  } else {
//    printf("=== NORMAL STATE MACHINE MODE ===\r\n");
  /* USER CODE END 2 */
  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */
    /* USER CODE BEGIN 3 */
    if (communication_test_mode) {
        static uint8_t test_initialized = 0;
        if (!test_initialized) {
            CAT1_PWR_ON;
            HAL_Delay(3000);
            if (gsm_init() == GSM_OK) {
											}
					 else {
											}

            if (gsm_gps_power_on() == GSM_OK) {
											}
					 else {
											}

            test_initialized = 1;
        }

        static uint32_t last_query_time = 0;
        uint32_t current_time = HAL_GetTick();
        if ((current_time - last_query_time) >= 1000) {
            GPS_UpdateAndDisplay();
            last_query_time = current_time;
        }

        HAL_Delay(100);
        continue;
    }

    static MasterState_t last_printed_state = (MasterState_t)255;
    if (current_state != last_printed_state) {
        last_printed_state = current_state;
    }

		if(INT_TEST==1){printf("INT1 Wake Trigger!\r\n");INT_TEST=0;}

		if(INT_TEST==2){printf("INT2 Wake Trigger!\r\n");INT_TEST=0;}

    switch (current_state) {
        case MASTER_STATE_WAKE_UP:
        {
            slave_device_id = 0;
            slave_battery_voltage = 0;
            saved_battery_voltage = 0;
            if (current_wakeup_source == WAKEUP_SOURCE_RTC) {
                printf("=== RTC Timer Wakeup ===\r\n");
                printf("Skipping E70 communication, using default slave data\r\n");
                current_state = MASTER_STATE_VOLTAGE_CHECK;
                rtc_wakeup_flag = 0;
                current_wakeup_source = WAKEUP_SOURCE_UNKNOWN;
											}
					 else {
                printf("=== GPIO External Wakeup ===\r\n");
                printf("Starting E70 communication with slave device\r\n");
                current_state = MASTER_STATE_E70_WAKE_SLAVE;
                e70_wake_retry_count = 0;
                current_wakeup_source = WAKEUP_SOURCE_UNKNOWN;
            }

            break;
        }

        case MASTER_STATE_E70_WAKE_SLAVE:
        {
            printf("=== E70 Wake Slave Phase ===\r\n");
            e70_wake_retry_count = 0;
            E70_EnterCommMode();
            HAL_Delay(100);
            extern volatile uint16_t uart_rx_index;
            extern volatile uint8_t new_data_received;
            extern volatile uint32_t interrupt_count;
            extern volatile uint8_t single_byte_buffer;
            extern volatile uint8_t uart_rx_buffer[];
            uart_rx_index = 0;
            new_data_received = 0;
            interrupt_count = 0;
            single_byte_buffer = 0;
            memset((void*)uart_rx_buffer, 0, 100);
            HAL_UART_AbortReceive_IT(&hlpuart1);
            uint32_t prev_err = HAL_UART_GetError(&hlpuart1);
            if (prev_err != HAL_UART_ERROR_NONE) {
            }

            __HAL_UART_CLEAR_OREFLAG(&hlpuart1);
            __HAL_UART_CLEAR_FEFLAG(&hlpuart1);
            __HAL_UART_CLEAR_NEFLAG(&hlpuart1);
            __HAL_UART_CLEAR_PEFLAG(&hlpuart1);
            HAL_Delay(2);
            E70_StartContinuousRx();
            printf("Sending wake signal to slave device...\r\n");
            uint8_t wake_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
            HAL_Delay(20);
            HAL_StatusTypeDef txs = HAL_UART_Transmit(&hlpuart1, wake_packet, sizeof(wake_packet), 200);
            if (txs != HAL_OK) {
            }
					 else {
											}

            extern volatile uint8_t new_data_received;
            if (new_data_received) {
            }

            HAL_Delay(50);
            e70_wake_start_time = HAL_GetTick();
            current_state = MASTER_STATE_WAIT_SLAVE_DATA;
            break;
        }

        case MASTER_STATE_WAIT_SLAVE_DATA:
        {
            extern volatile uint8_t new_data_received;
            extern volatile uint8_t uart_rx_buffer[];
            extern volatile uint16_t uart_rx_index;
            extern volatile uint8_t single_byte_buffer;
            static uint16_t last_processed_index = 0;
            static uint8_t state_first_entry = 1;
            if (state_first_entry) {
                last_processed_index = 0;
                state_first_entry = 0;
            }

            if (uart_rx_index > last_processed_index) {
                if (uart_rx_index >= 7) {
                    for (uint16_t i = 0; i <= uart_rx_index - 7; i++) {
                        if (uart_rx_buffer[i] == 0xFA && uart_rx_buffer[i+1] == 0xFB && uart_rx_buffer[i+6] == 0xFD) {
                            slave_device_id = (uart_rx_buffer[i+2] << 8) | uart_rx_buffer[i+3];
                            slave_battery_voltage = (uart_rx_buffer[i+4] << 8) | uart_rx_buffer[i+5];
                            printf("Slave ID: 0x%04X, Battery: %dmV\r\n", slave_device_id, slave_battery_voltage);
                            uart_rx_index = 0;
                            last_processed_index = 0;
                            new_data_received = 0;
                            E70_StopContinuousRx();
                            state_first_entry = 1;
                            current_state = MASTER_STATE_SEND_ACK;
                            break;
                        }

                    }

                }

                if (uart_rx_index > 0) {
                    last_processed_index = uart_rx_index;
                }

            }

            uint32_t state = HAL_UART_GetState(&hlpuart1);
            if (!(state & HAL_UART_STATE_BUSY_RX)) {
                HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
            }

            if ((HAL_GetTick() - e70_wake_start_time) > 1000) {
                e70_wake_retry_count++;
                printf("Wake timeout. Retry count: %d\r\n", e70_wake_retry_count);
                if (e70_wake_retry_count < 20) {
                    uint8_t wake_packet[] = {0xFC, 0xFD, 0x55, 0x66, 0xFD};
                    HAL_UART_Transmit(&hlpuart1, wake_packet, sizeof(wake_packet), 10);
                    e70_wake_start_time = HAL_GetTick();
                }
								else {
                    printf("E70 communication failed after %d retries\r\n", e70_wake_retry_count);
                    E70_StopContinuousRx();
                    RF_PWR_OFF;
                    state_first_entry = 1;
                    current_state = MASTER_STATE_PREPARE_SLEEP;
                }

            }

            break;
        }

        case MASTER_STATE_SEND_ACK:
        {
            E70_StopContinuousRx();
            HAL_Delay(20);
            uint8_t ack_packet[] = {0xFC, 0xFD, 0x77, 0x88, 0xFD};
            HAL_StatusTypeDef acks = HAL_UART_Transmit(&hlpuart1, ack_packet, sizeof(ack_packet), 1000);
            if (acks == HAL_OK) {
            }
						else {
            }

            HAL_Delay(80);
            RF_PWR_OFF;
            current_state = MASTER_STATE_VOLTAGE_CHECK;
            break;
        }

        case MASTER_STATE_VOLTAGE_CHECK:
        {
						RF_PWR_OFF;
            HAL_Delay(200);
            saved_battery_voltage = ADC_ReadBatteryVoltage();
            float battery_voltage_v = saved_battery_voltage / 1000.0f;
            printf("Master Battery Voltage: %.3fV (%dmV) - Saved\r\n", battery_voltage_v, saved_battery_voltage);
            current_state = MASTER_STATE_CAT1_GPS;
            break;
        }

        case MASTER_STATE_CAT1_GPS:
        {
					static uint8_t gps_state_initialized = 0;
					static uint8_t test_initialized = 0;
					static uint8_t state_entry_flag = 1;
					if (state_entry_flag) {
					printf("=== CAT1 GPS Phase ===\r\n");
					gps_state_initialized = 0;
					test_initialized = 0;
					state_entry_flag = 0;
					}

					if (!gps_state_initialized) {
					gps_state_initialized = 1;
					}

					if (!test_initialized) {
					printf("CAT1/GPS Mode Started\r\n");
					CAT1_PWR_ON;
					HAL_Delay(3000);
					if (gsm_init() == GSM_OK) {
					printf("GSM init OK\r\n");
					}
					else {
					printf("GSM init FAIL\r\n");
					current_state = MASTER_STATE_PREPARE_SLEEP;
					break;
					}

					if (gsm_gps_power_on() == GSM_OK) {
					printf("Send AT+CGNSPWR=1 OK\r\n");
					}
					else {
					printf("Send AT+CGNSPWR=1 FAIL\r\n");
					current_state = MASTER_STATE_PREPARE_SLEEP;
					break;
					}

					test_initialized = 1;
					}

					static uint32_t last_query_time = 0;
					static uint32_t gps_start_time = 0;
					uint32_t current_time = HAL_GetTick();
					if (state_entry_flag == 0 && gps_start_time == 0) {
					gps_start_time = current_time;
					last_query_time = 0;
					}

					uint32_t elapsed_time = (current_time - gps_start_time) / 1000;
					if ((current_time - last_query_time) >= 1000) {
					printf("GPS positioning... %d seconds elapsed\r\n", elapsed_time);
					GPS_Status_t gps_status = GPS_QueryAIR780EG();
					if (gps_status == GPS_OK && GPS_IsDataReady()) {
					extern GPS_Data_t gps_data;
					printf("=== GPS Information ===\r\n");
					if (gps_data.valid) {
					printf("Getting CCID...\r\n");
					if (gsm_get_ccid(global_ccid) == GSM_OK) {
					}
					else {
					printf("Failed to get CCID\r\n");
					strcpy(global_ccid, "UNKNOWN");
					}

					uint8_t rssi, ber;
					printf("Getting signal strength...\r\n");
					if (gsm_get_signal(&rssi, &ber) == GSM_OK) {
					global_signal_dbm = (int8_t)rssi;
					}
					else {
					global_signal_dbm = -128;
					rssi = 0;
					ber = 99;
					}

					gsm_gps_power_off();
					GPS_ClearDataReadyFlag();
					state_entry_flag = 1;
					gps_start_time = 0;
					last_query_time = 0;
					current_state = MASTER_STATE_HELLO_TEST;
					break;
					}
					else {
					printf("GPS still searching satellites... (Count: %d)\r\n", gps_data.satellites);
					}

					GPS_ClearDataReadyFlag();
					}
					else {
					}

					last_query_time = current_time;
					}

					if (elapsed_time >= 120) {
					printf("GPS data acquisition timeout (2 minutes). Using default location data...\r\n");
					gsm_gps_power_off();
					printf("Getting CCID...\r\n");
					if (gsm_get_ccid(global_ccid) == GSM_OK) {
					}
					else {
					printf("Failed to get CCID\r\n");
					strcpy(global_ccid, "UNKNOWN");
					}

					uint8_t rssi, ber;
					printf("Getting signal strength...\r\n");
					if (gsm_get_signal(&rssi, &ber) == GSM_OK) {
					global_signal_dbm = (int8_t)rssi;
					}
					else {
					global_signal_dbm = -128;
					rssi = 0;
					ber = 99;
					}

					GPS_ClearDataReadyFlag();
					state_entry_flag = 1;
					gps_start_time = 0;
					last_query_time = 0;
					printf("GPS timeout handled, continuing to data upload with default location...\r\n");
					current_state = MASTER_STATE_HELLO_TEST;
					break;
					}

					HAL_Delay(100);
					break;
					}

					case MASTER_STATE_HELLO_TEST:
					{
					printf("=== RTC Sync Phase ===\r\n");
					RTC_PrintCurrentTime("RTC before sync: ");
					if (GPS_SyncRTC(&gps_data)) {
					printf("RTC sync successful!\r\n");
					RTC_PrintCurrentTime("RTC after sync:  ");
					}
					else {
					printf("RTC sync failed (GPS data invalid)\r\n");
					}

					printf("=========================\r\n");
					GPS_GenerateHYDataPacket(saved_battery_voltage, slave_device_id, slave_battery_voltage);
					current_state = MASTER_STATE_CAT1_UPLOAD;
					break;
        }

        case MASTER_STATE_CAT1_UPLOAD:
        {
					printf("=== CAT1 Upload Phase ===\r\n");
					gsm_status_t connect_status = gsm_connect_default_server();
					if (connect_status == GSM_OK) {
					HAL_Delay(3000);
					printf("Sending real-time GPS data...\r\n");
					char response[64];
					extern char global_data_packet[256];
					char* packet_ptr = GPS_GenerateHYDataPacketString(saved_battery_voltage, slave_device_id, slave_battery_voltage, global_data_packet);
					if (packet_ptr == NULL) {
					printf("Failed to generate GPS data packet\r\n");
					CAT1_PWR_OFF;
					current_state = MASTER_STATE_PREPARE_SLEEP;
					break;
					}

					printf("Real-time data length: %d bytes\r\n", strlen(packet_ptr));
					const char* realtime_data = packet_ptr;
					gsm_status_t send_status = gsm_send_data(realtime_data, response);
					if (send_status == GSM_OK) {
					uint16_t history_count = Flash_GetHistoryDataCount();
					if (history_count > 0) {
					printf("Found %d history records, sending...\r\n", history_count);
					extern char global_data_packet[256];
					uint16_t sent_count = 0;
					uint16_t failed_count = 0;
					while (Flash_GetHistoryDataCount() > 0 && sent_count < 10) {
					if (Flash_GetNextHistoryData(global_data_packet, sizeof(global_data_packet)) == HAL_OK) {
					printf("Sending history[%d]: %s\r\n", sent_count, global_data_packet);
					gsm_status_t history_send_status = gsm_send_data(global_data_packet, response);
					if (history_send_status == GSM_OK) {
					printf("History[%d] sent OK\r\n", sent_count);
					sent_count++;
					}
					else {
					printf("History[%d] send failed, re-saving...\r\n", sent_count);
					Flash_SaveHistoryData(global_data_packet);
					failed_count++;
					break;
					}

					HAL_Delay(1000);
					}
					else {
					break;
					}

					}

					printf("History data summary: sent=%d, failed=%d, remaining=%d\r\n",
					sent_count, failed_count, Flash_GetHistoryDataCount());
					}
					else {
					}

					}
					else {
					printf("Real-time data send failed, saving to history...\r\n");
					Flash_SaveHistoryData(realtime_data);
					}

					gsm_tcp_close();
					}
					else {
					printf("Server connection failed, saving data to history...\r\n");
					extern char global_data_packet[256];
					char* packet_ptr = GPS_GenerateHYDataPacketString(saved_battery_voltage, slave_device_id, slave_battery_voltage, global_data_packet);
					if (packet_ptr != NULL) {
					Flash_SaveHistoryData(packet_ptr);
					}

					}

					CAT1_PWR_OFF;
					current_state = MASTER_STATE_PREPARE_SLEEP;
					break;
					}

        case MASTER_STATE_PREPARE_SLEEP:
        {
            goto enter_sleep_mode;
        }

        default:
        {
            printf("ERROR: Unknown state %d\r\n", current_state);
            current_state = MASTER_STATE_PREPARE_SLEEP;
            break;
        }

    }

    continue;
    enter_sleep_mode:
    /* 进入休眠模式处理 */
    {
        GPIO_InitTypeDef GPIO_InitStruct = {0};
        printf("Preparing to enter STOP mode...\r\n");
        interrupt_enabled = 1;
#if ENABLE_RTC_WAKEUP
        HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);
        __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
        __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();
        extern uint32_t user_sleep_seconds;
        uint32_t sleep_seconds = (user_sleep_seconds > 0) ? user_sleep_seconds : RTC_WAKEUP_INTERVAL_SECONDS;
        const uint32_t MAX_SLEEP_SECONDS = 18 * 3600;
        if (sleep_seconds > MAX_SLEEP_SECONDS) {
            printf("Sleep time limited to maximum: %lu seconds (18 hours)\r\n", MAX_SLEEP_SECONDS);
            sleep_seconds = MAX_SLEEP_SECONDS;
        }

        if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, sleep_seconds-1, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) == HAL_OK) {
            printf("RTC wakeup timer set for %lu seconds\r\n", sleep_seconds);
        }
        else {
            printf("Failed to set RTC wakeup timer\r\n");
        }

#endif
        HAL_Delay(50);
        RF_PWR_OFF;
        CAT1_PWR_OFF;
        LED_OFF;
        HAL_UART_AbortReceive_IT(&huart1);
        HAL_UART_AbortReceive_IT(&hlpuart1);
        HAL_UART_AbortReceive_IT(&huart4);
        HAL_UART_DeInit(&hlpuart1);
        HAL_ADC_Stop_DMA(&hadc);
        HAL_ADC_Stop(&hadc);
        if(hadc.DMA_Handle != NULL) {
            HAL_DMA_Abort(hadc.DMA_Handle);
        }

        GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
        GPIO_InitStruct.Pin = GPIO_PIN_2 | GPIO_PIN_3;
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
        GPIO_InitStruct.Pull = GPIO_PULLDOWN;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
        __HAL_RCC_USART1_CLK_DISABLE();
        __HAL_RCC_LPUART1_CLK_DISABLE();
        __HAL_RCC_USART4_CLK_DISABLE();
        __HAL_RCC_ADC1_CLK_DISABLE();
        __HAL_RCC_DMA1_CLK_DISABLE();
        HAL_Delay(50);
        __disable_irq();
        SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;
        for (uint8_t i = 0; i < 8; i++) {
            NVIC->ICER[i] = 0xFFFFFFFF;
        }

        HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);
#if ENABLE_RTC_WAKEUP
        HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(RTC_IRQn);
        __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
        __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();
#endif
        for (uint8_t i = 0; i < 8; i++) {
            NVIC->ICPR[i] = 0xFFFFFFFF;
        }

        __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);
        __enable_irq();
        HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
        LED_ON;
        if (rtc_wakeup_flag) {
            printf("*** Device woken up by RTC timer ***\r\n");
            printf("RTC wakeup flag: %d, current_wakeup_source: %d\r\n", rtc_wakeup_flag, current_wakeup_source);
        }
 else {
            printf("*** Device woken up by external trigger ***\r\n");
            printf("RTC wakeup flag: %d, current_wakeup_source: %d\r\n", rtc_wakeup_flag, current_wakeup_source);
        }

        SystemClock_Config();
        SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;
        __HAL_RCC_USART1_CLK_ENABLE();
        __HAL_RCC_LPUART1_CLK_ENABLE();
        __HAL_RCC_USART4_CLK_ENABLE();
        __HAL_RCC_ADC1_CLK_ENABLE();
        __HAL_RCC_DMA1_CLK_ENABLE();
        MX_GPIO_Init();
        MX_USART1_UART_Init();
        MX_LPUART1_UART_Init();
        MX_USART4_UART_Init();
        MX_ADC_Init();
        HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
        HAL_NVIC_EnableIRQ(USART1_IRQn);
        HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
        HAL_NVIC_EnableIRQ(LPUART1_IRQn);
        HAL_Delay(50);
        /* 重新启用GPIO中断 */
        HAL_NVIC_SetPriority(EXTI4_15_IRQn, 0, 0);
        HAL_NVIC_EnableIRQ(EXTI4_15_IRQn);
        GPIO_InitStruct.Pin = M0_Pin | M1_Pin | M2_Pin;
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
        GPIO_InitStruct.Pull = GPIO_NOPULL;
        GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
        HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
        HAL_GPIO_WritePin(GPIOA, M2_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOA, M1_Pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(GPIOA, M0_Pin, GPIO_PIN_SET);
        /* LED闪烁表示正在唤醒 */
        LED_TOGGLE;
        HAL_Delay(100);
        LED_TOGGLE;
        HAL_Delay(100);
        LED_TOGGLE;
        HAL_Delay(100);
        LED_ON;
        /* 重置状态机到唤醒状态 */
        current_state = MASTER_STATE_WAKE_UP;
    }

  }

  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0}
;
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0}
;
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0}
;
  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);
  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI|RCC_OSCILLATORTYPE_LSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLLMUL_4;
  RCC_OscInitStruct.PLL.PLLDIV = RCC_PLLDIV_4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }

  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART1|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_RTC;
  PeriphClkInit.Usart1ClockSelection = RCC_USART1CLKSOURCE_PCLK2;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }

}

/* USER CODE BEGIN 4 */
/**
  * @brief GPIO外部中断回调函数
  * @param GPIO_Pin: 触发中断的引脚
  * @retval None
  */
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
  if (!interrupt_enabled) {
    return;
  }

  if (GPIO_Pin == INT1_Pin)
  {
    INT_TEST=1;
    current_wakeup_source = WAKEUP_SOURCE_GPIO;
    interrupt_enabled = 0;
  }

  else if (GPIO_Pin == INT2_Pin)
  {
		INT_TEST=2;
    current_wakeup_source = WAKEUP_SOURCE_GPIO;
    interrupt_enabled = 0;
  }

}

/**
  * @brief RTC唤醒定时器事件回调函数
  * @param hrtc: RTC句柄指针
  * @retval None
  */
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
    rtc_wakeup_flag = 1;
    current_wakeup_source = WAKEUP_SOURCE_RTC;
    __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);
    __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();
}

/**
 * @brief 打印历史数据状态
 */
void PrintHistoryDataStatus(void)
{
    uint16_t count = Flash_GetHistoryDataCount();
    printf("=== History Data Status ===\r\n");
    printf("Total history records: %d\r\n", count);
    printf("Max capacity: 200 records\r\n");
    printf("Storage usage: %.1f%%\r\n", (float)count * 100.0f / 200.0f);
    printf("==========================\r\n");
}

/**
 * @brief 测试读取历史数据
 */
void TestHistoryDataRead(void)
{
    printf("=== Test History Data Read ===\r\n");
    extern char global_data_packet[256];
    uint16_t read_count = 0;
    while (Flash_GetHistoryDataCount() > 0 && read_count < 5) {
        if (Flash_GetNextHistoryData(global_data_packet, sizeof(global_data_packet)) == HAL_OK) {
            printf("History[%d]: %s\r\n", read_count, global_data_packet);
            read_count++;
        }
 else {
            break;
        }

    }

    printf("Read %d history records\r\n", read_count);
    printf("Remaining: %d records\r\n", Flash_GetHistoryDataCount());
    printf("=============================\r\n");
}

/* USER CODE END 4 */
/**
  * @brief  Period elapsed callback in non blocking mode
  * @note   This function is called  when TIM6 interrupt took place, inside
  * HAL_TIM_IRQHandler(). It makes a direct call to HAL_IncTick() to increment
  * a global variable "uwTick" used as application time base.
  * @param  htim : TIM handle
  * @retval None
  */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  /* USER CODE BEGIN Callback 0 */
  /* USER CODE END Callback 0 */
  if (htim->Instance == TIM6)
  {
    HAL_IncTick();
  }

  /* USER CODE BEGIN Callback 1 */
  /* USER CODE END Callback 1 */
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }

  /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}

#endif /* USE_FULL_ASSERT */
