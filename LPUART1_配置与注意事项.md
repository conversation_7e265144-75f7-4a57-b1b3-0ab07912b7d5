# LPUART1 串口配置与注意事项

## 1. 基本配置

### 1.1 硬件配置

```c
hlpuart1.Instance = LPUART1;
hlpuart1.Init.BaudRate = 9600;
hlpuart1.Init.WordLength = UART_WORDLENGTH_8B;
hlpuart1.Init.StopBits = UART_STOPBITS_1;
hlpuart1.Init.Parity = UART_PARITY_NONE;
hlpuart1.Init.Mode = UART_MODE_TX_RX;
hlpuart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
hlpuart1.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
hlpuart1.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
```

### 1.2 中断配置

```c
// 启用UART中断 - 确保E70通信正常
HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
HAL_NVIC_EnableIRQ(LPUART1_IRQn);
```

## 2. 关键变量

```c
// 大缓存持续接收
volatile uint8_t uart_rx_buffer[100];  // 接收缓冲区
volatile uint16_t uart_rx_index = 0;   // 接收索引
volatile uint8_t new_data_received = 0; // 新数据接收标志
volatile uint8_t single_byte_buffer = 0; // 单字节接收缓冲区

// 调试计数器
volatile uint32_t interrupt_count = 0; // 中断计数器
```

## 3. 中断接收处理

### 3.1 中断回调函数

```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == LPUART1) {
        // 增加中断计数器
        interrupt_count++;

        // 存入缓存，不清除，持续累积
        if (uart_rx_index < 100) {
            uart_rx_buffer[uart_rx_index] = single_byte_buffer;
            uart_rx_index++;
        }

        // 设置接收标志
        new_data_received = 1;

        // 继续接收下一个字节
        HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);

        last_rx_tick = HAL_GetTick();
    }
}
```

### 3.2 启动中断接收

```c
// 启动接收（统一通过该函数启动，避免重复 HAL_UART_Receive_IT）
E70_StartContinuousRx();
// 注意：不要在此处再次调用 HAL_UART_Receive_IT 避免重复启动
```

## 4. 工作流程与关键点（母设备/状态机模式）

- 上电/唤醒后流程：
  1) 进入通信模式：E70_SetModeWithPowerCycle(E70_MODE_TRANS)（断电→设M2/M1/M0→上电→1s延时）
  2) 清空接收状态：清零 uart_rx_index/new_data_received/single_byte_buffer，Abort 残留接收，清 UART 错误标志
  3) 启动持续接收：E70_StartContinuousRx()（内部调用 HAL_UART_Receive_IT 单字节循环）
  4) 主机发送唤醒帧：FC FD 55 66 FD
  5) 等待并识别从机数据：FA FB [ID高] [ID低] [电压高] [电压低] FD
  6) 主机回复确认帧：FC FD 77 88 FD（先停止接收→发送→适当延时→必要时再恢复接收）
  7) 关闭E70并进入后续环节（测电压/休眠等）

- 关键点：
  - 半双工收发切换：发送前先 E70_StopContinuousRx()，发送后延时一段时间（建议20~80ms），再按需要恢复接收
  - STOP唤醒后的串口恢复：进入STOP前 HAL_UART_DeInit(&hlpuart1)，唤醒后重新 MX_LPUART1_UART_Init()
  - 模式脚时序：E70上电前，M0=1,M1=0,M2=0（TRANS=001），上电后至少延时1000ms
  - UART错误清理：在重启接收前清 ORE/FE/NE/PE，避免残留错误导致收发异常
  - 发送前轻微延时：在 HAL_UART_Transmit 之前适当 HAL_Delay(20) 可提升稳定性

## 5. 数据包处理

### 4.1 数据包检测与提取

```c
// 检查是否包含特定格式的数据包（FA FB 00 01 0C E4 FD）
// 确保有足够的数据进行检查
if (uart_rx_index >= 7) {
    for (uint16_t i = last_processed_index; i <= uart_rx_index - 7; i++) {
        if (uart_rx_buffer[i] == 0xFA && uart_rx_buffer[i+1] == 0xFB && uart_rx_buffer[i+6] == 0xFD) {
            // 找到一个完整的数据包，提取并打印
            printf("Data Packet: ");
            for (uint16_t j = i; j <= i+6; j++) {
                printf("%02X ", uart_rx_buffer[j]);
            }
            printf("\r\n");

            // 清理缓存并重新开始接收
            uart_rx_index = 0;
            last_processed_index = 0;
            new_data_received = 0;

            // 重新启动接收
            HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
            break;
        }
    }
}
```

### 4.2 状态检查与恢复

```c
// 检查UART状态并确保中断接收正常
uint32_t state = HAL_UART_GetState(&hlpuart1);
if (!(state & HAL_UART_STATE_BUSY_RX)) {
    HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
    printf("[ALERT] LPUART1 interrupt restarted\r\n");
}
```

## 5. 常见问题与解决方案

### 5.1 编译错误：变量未定义

**问题描述**：编译时出现`uart_rx_index`、`new_data_received`、`interrupt_count`和`single_byte_buffer`等变量未定义的错误。

**解决方案**：在使用这些变量之前，添加外部变量声明：

```c
extern volatile uint8_t new_data_received;
extern volatile uint8_t uart_rx_buffer[];
extern volatile uint16_t uart_rx_index;
extern volatile uint32_t interrupt_count;
extern volatile uint8_t single_byte_buffer;
```

### 5.2 中断接收停止

**问题描述**：在某些情况下，LPUART1的中断接收可能会停止，导致无法接收新数据。

**解决方案**：定期检查UART状态，如果中断接收未启动，则重新启动：

```c
uint32_t state = HAL_UART_GetState(&hlpuart1);
if (!(state & HAL_UART_STATE_BUSY_RX)) {
    HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
    printf("[ALERT] LPUART1 interrupt restarted\r\n");
}
```

### 5.3 重复处理数据

**问题描述**：如果不正确更新处理索引，可能会导致重复处理相同的数据。

**解决方案**：正确更新处理索引，确保只处理新接收的数据：

```c
// 如果没有找到完整数据包，更新处理索引
if (uart_rx_index > 0) {
    last_processed_index = uart_rx_index;
}
```

### 5.4 缓冲区溢出

**问题描述**：如果接收缓冲区满了，新数据将无法存储。

**解决方案**：在找到完整数据包后清理缓冲区，或者定期检查缓冲区大小，如果接近满，则清理：

```c
// 在测试模式下可不立即清空大缓存，只更新 last_processed_index
// 状态机模式下识别并处理后可以直接进入ACK阶段，不依赖保留缓存
// 清理策略按模式而定```

## 6. 修复过程总结

### 6.1 通信测试模式修复（已完成）

1. **添加缺失的外部变量声明**：在通信测试模式中，添加了`uart_rx_index`、`new_data_received`、`interrupt_count`和`single_byte_buffer`等变量的外部声明，解决了编译错误。

2. **优化数据包检测与提取**：修改了数据包检测逻辑，只提取特定格式的数据包（FA FB 00 01 0C E4 FD），并在成功提取后清理缓存重新接收。

3. **简化状态打印**：移除了冗余的状态打印，只保留必要的信息，使输出更清晰。

4. **增强中断接收稳定性**：添加了UART状态检查和自动重启机制，确保中断接收始终处于活动状态。

5. **修复处理索引更新逻辑**：修正了处理索引更新逻辑，防止重复处理相同数据。

6. **优化缓冲区管理**：在成功提取数据包后清理缓冲区，防止缓冲区溢出。

### 6.2 状态机模式修复（最新完成）

1. **统一接收逻辑**：将状态机中的数据接收逻辑修改为与通信测试模式完全一致，使用相同的变量名和处理流程。

2. **修复变量不一致问题**：状态机原本使用了`data_received_flag`、`actual_data_length`等未定义变量，现已改为使用`uart_rx_index`、`new_data_received`等已验证的变量。

3. **添加数据解析功能**：在状态机中添加了子设备数据解析功能，正确提取子设备ID和电池电压信息。

4. **完善接收初始化**：在MASTER_STATE_E70_WAKE_SLAVE状态中，添加了与通信测试模式相同的接收变量初始化和手动中断启动。

5. **增强接收稳定性**：在MASTER_STATE_WAIT_SLAVE_DATA状态中，添加了UART状态检查和自动重启机制。

6. **优化数据包检测**：使用与通信测试模式相同的数据包检测算法，确保能够正确识别和提取完整的数据包。

### 6.3 数据接收调试优化（2024-12-19）

**问题描述：**
用户反馈状态机模式下虽然接收到数据（index增长），但无法正确识别FA FB格式的数据包。

**修复内容：**
1. **添加原始数据打印：** 在接收到新数据时打印所有原始字节，便于调试分析
2. **修复边界条件：** 修复数据包检测循环的边界条件，确保能检查到所有可能的7字节数据包位置
3. **切换到状态机模式：** 将 `communication_test_mode` 设置为0，启用状态机模式进行测试

**调试输出格式：**
```
New data received (index X to Y): AA BB CC DD ...
```
这样可以清楚看到实际接收到的数据内容，便于分析数据包格式问题。

### 6.4 休眠唤醒后收发问题修复（2025-01-xx）

**问题描述：**
设备初次上电时可以正确接收数据，但休眠唤醒后只能接收到单个字节（如`00`），无法接收完整的7字节数据包。经过深入分析发现，主要问题是重试计数器未重置导致的逻辑错误。

**根本原因分析：**
1. **重试计数器未重置：** `e70_wake_retry_count` 在休眠唤醒后保持之前的值，可能已达到最大重试次数
2. **数据包检测逻辑错误：** 原来的 `E70_CheckReceiveSlaveData` 和 `E70_CheckReceiveAck` 函数依赖 `data_received_flag`，但这个标志设置逻辑有问题
3. **缓冲区状态残留：** 休眠唤醒后，接收缓冲区和状态变量没有完全清理
4. **处理索引未重置：** `last_processed_index` 静态变量在新的唤醒周期中保持旧值
5. **UART状态不一致：** 可能存在残留的UART接收状态
6. **E70模块稳定时间不足：** 断电重启后稳定时间可能不够

**修复内容：**
1. **重置唤醒重试计数器：** 在每次唤醒周期开始时重置 `e70_wake_retry_count` 为0
2. **完全清理接收状态：** 在E70模块重新上电后，清零接收相关变量和缓冲区；Abort 残留接收；清 ORE/FE/NE/PE 错误
3. **统一接收入口：** 仅通过 `E70_StartContinuousRx()` 启动接收，避免重复 HAL_UART_Receive_IT
4. **发送稳定性：** 停止接收→发送→延时→再按需要恢复接收（半双工切换窗口）
5. **STOP唤醒恢复：** STOP前 `HAL_UART_DeInit(&hlpuart1)`，唤醒后重新 `MX_LPUART1_UART_Init()`

### 6.5 数据包检测逻辑优化（2024-12-19）

**问题描述：**
之前的修复引入了新问题，导致连测试代码也无法正确接收完整数据包，只能收到前3字节 `FA FB E4`。

**根本原因：**
在 `HAL_UART_RxCpltCallback` 中添加的7字节数据包检测逻辑干扰了正常的数据接收流程，导致数据包接收不完整。

**最终修复方案：**
1. **撤销中断回调中的数据包检测：** 移除 `HAL_UART_RxCpltCallback` 中的7字节数据包检测逻辑，恢复简单的字节接收和缓存
2. **优化数据包检测函数：** 修改 `E70_CheckReceiveSlaveData` 和 `E70_CheckReceiveAck` 函数，使其在缓冲区中搜索完整数据包，而不依赖特定的标志位
3. **统一检测逻辑：** 确保状态机和测试模式使用相同的数据包检测逻辑
6. **重置处理索引：** 每次进入 `MASTER_STATE_WAIT_SLAVE_DATA` 状态时重置 `last_processed_index`
7. **状态标志管理：** 添加 `state_first_entry` 标志确保每次唤醒周期都正确重置
8. **增加E70稳定延时：** 在E70模块上电后增加1000ms延时确保完全稳定
9. **添加详细调试信息：** 在UART接收回调中添加详细的字节级调试输出

### 6.6 测试代码成功经验总结（2024-12-19）

**关键发现：中断回调中不能使用printf**

**重要问题**：在 `HAL_UART_RxCpltCallback` 中使用 `printf` 会导致数据接收丢失。

**原因分析**：
- `printf` 函数在中断上下文中执行时间过长
- 影响后续中断响应的及时性
- 导致正在接收的数据被截断或丢失

**解决方案**：
```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == LPUART1) {
        interrupt_count++;

        if (uart_rx_index < 100) {
            uart_rx_buffer[uart_rx_index] = single_byte_buffer;
            uart_rx_index++;

            // 注释掉printf调试信息，避免在中断中使用printf导致数据丢失
            // printf("UART RX[%d]: 0x%02X\r\n", uart_rx_index-1, single_byte_buffer);
        }

        new_data_received = 1;
        HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
        last_rx_tick = HAL_GetTick();
    }
}
```

**测试代码数据包检测优化**

**成功的缓存管理策略**：
1. **不立即清理缓存**：找到数据包后不立即清理缓存，而是更新处理索引
2. **智能缓存清理**：只在缓存接近满载时保留最后20字节数据
3. **避免中断干扰**：不在数据接收过程中频繁重启UART中断

**优化后的检测逻辑**：
```c
// 检查是否包含特定格式的数据包（FA FB xx xx xx xx FD）
if (uart_rx_index >= 7) {
    for (uint16_t i = last_processed_index; i <= uart_rx_index - 7; i++) {
        if (uart_rx_buffer[i] == 0xFA && uart_rx_buffer[i+1] == 0xFB && uart_rx_buffer[i+6] == 0xFD) {
            // 解析设备ID和电池电压
            uint16_t device_id = (uart_rx_buffer[i+2] << 8) | uart_rx_buffer[i+3];
            uint16_t battery_voltage = (uart_rx_buffer[i+4] << 8) | uart_rx_buffer[i+5];
            printf("Device ID: 0x%04X, Battery: %d mV\r\n", device_id, battery_voltage);

            // 更新处理索引，但不清理缓存
            last_processed_index = i + 7;
            break;
        }
    }
}
```

**中断服务程序最佳实践**

**应避免在ISR中执行的操作**：
- 使用 `printf` 等耗时的I/O操作
- 执行复杂的数据处理逻辑
- 调用可能阻塞的函数
- 进行长时间的循环操作

**ISR中应该做的**：
- 快速保存接收到的数据
- 设置标志位通知主循环
- 立即重新启动下一次接收
- 更新时间戳等简单操作

**测试结果验证**

**成功指标**：
- 能够正确接收完整的7字节数据包：`FA FB 00 01 0C E4 FD`
- 正确解析设备ID：`0x0001`
- 正确解析电池电压：`3300 mV`
- 持续稳定接收，无数据丢失

**测试日志示例**：
```
Data Packet: FA FB 00 01 0C E4 FD
Device ID: 0x0001, Battery: 3300 mV
```

**关键修复代码：**

1. **重置唤醒重试计数器**（在 `MASTER_STATE_E70_WAKE_SLAVE` 状态中）：
```c
case MASTER_STATE_E70_WAKE_SLAVE:
{
    printf("=== E70 Wake Slave Phase ===\r\n");

    // 重置唤醒重试计数器 - 关键修复
    e70_wake_retry_count = 0;
    printf("Reset wake retry count to 0\r\n");

    // ... 其他代码
}
```

2. **优化数据包检测函数**（修改 `E70_CheckReceiveSlaveData`）：
```c
uint8_t E70_CheckReceiveSlaveData(uint16_t *device_id, uint16_t *battery_voltage)
{
    // 检查是否有足够的数据进行7字节数据包检测
    if (uart_rx_index >= 7) {
        // 在缓冲区中搜索完整的7字节数据包 (FA FB xx xx xx xx FD)
        for (uint16_t i = 0; i <= uart_rx_index - 7; i++) {
            if (uart_rx_buffer[i] == E70_SEND_HEADER1 &&      // 0xFA
                uart_rx_buffer[i+1] == E70_SEND_HEADER2 &&    // 0xFB
                uart_rx_buffer[i+6] == E70_PACKET_END) {      // 0xFD

                // 解析设备ID和电池电压
                *device_id = (uart_rx_buffer[i+2] << 8) | uart_rx_buffer[i+3];
                *battery_voltage = (uart_rx_buffer[i+4] << 8) | uart_rx_buffer[i+5];

                return 1;
            }
        }
    }
    return 0;
}
```

3. **简化中断回调函数**（移除干扰逻辑）：
```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == LPUART1) {
        // 简单的字节接收和缓存，不进行数据包检测
        if (uart_rx_index < 100) {
            uart_rx_buffer[uart_rx_index] = single_byte_buffer;
            uart_rx_index++;
        }
        new_data_received = 1;
        HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&single_byte_buffer, 1);
    }
}
```

**调试信息增强：**
```c
// 在UART接收回调中添加详细调试
printf("UART RX[%d]: 0x%02X (interrupt_count=%lu)\r\n",
       uart_rx_index-1, single_byte_buffer, interrupt_count);
```

## 7. 模式切换说明

### 7.1 切换方法

在main.c文件中，通过修改`communication_test_mode`变量来切换工作模式：

```c
// 通信测试模式控制变量
// 修改这个值来切换模式：1=通信测试模式，0=正常状态机模式
static uint8_t communication_test_mode = 1;    // 1=测试模式，0=状态机模式
```

### 7.2 模式说明

- **通信测试模式 (communication_test_mode = 1)**：
  - 用于验证LPUART1通信功能
  - 每1秒发送测试数据包：FC FD 55 66 FD
  - 检测并显示接收到的数据包：FA FB 00 01 0C E4 FD
  - 适用于通信功能调试和验证

- **状态机模式 (communication_test_mode = 0)**：
  - 正常的母设备业务流程
  - 包含唤醒、通信、数据处理、休眠等完整流程
  - 适用于实际产品运行

### 7.3 切换注意事项

1. **重新编译**：修改模式后需要重新编译和下载程序
2. **接收逻辑一致**：两种模式现在使用相同的接收逻辑，确保通信稳定性
3. **调试输出**：两种模式都有详细的调试输出，便于问题定位

## 8. 注意事项

1. **变量声明顺序**：确保在使用外部变量之前进行声明。

2. **中断优先级**：LPUART1中断优先级设置为3，确保不会干扰其他更重要的中断。

3. **缓冲区大小**：接收缓冲区大小为100字节，根据实际需求可能需要调整。

4. **中断接收启动**：在初始化和每次接收完成后，都需要重新启动中断接收。

5. **状态检查**：定期检查UART状态，确保中断接收正常工作。

6. **数据包格式**：当前代码专门检测格式为FA FB 00 01 0C E4 FD的数据包，如需检测其他格式，需修改检测逻辑。

7. **清理缓存时机**：在成功提取数据包后清理缓存，避免缓冲区溢出和数据混淆。
