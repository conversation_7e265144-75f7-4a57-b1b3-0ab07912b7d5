/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    rtc_config.h
  * @brief   RTC时钟修正配置文件
  ******************************************************************************
  * @attention
  *
  * RTC时钟修正配置，用于补偿LSI时钟源的精度问题
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#ifndef __RTC_CONFIG_H
#define __RTC_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* RTC时钟修正配置 */
#define RTC_CLOCK_CORRECTION_FACTOR 1.17f  // LSI时钟修正系数，补偿负偏差
#define RTC_MAX_SLEEP_SECONDS_RAW 65535     // RTC硬件最大计数值

/* 计算在修正系数下能设置的最大逻辑时间 */
/* 65535 / 1.17 ≈ 55982，手动计算避免编译时浮点运算 */
#define RTC_MAX_LOGICAL_SLEEP_SECONDS 55982U

/* 函数声明 */
uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds);

#ifdef __cplusplus
}
#endif

#endif /* __RTC_CONFIG_H */
