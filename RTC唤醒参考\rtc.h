/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    rtc.h
  * @brief   This file contains all the function prototypes for
  *          the rtc.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __RTC_H__
#define __RTC_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

extern RTC_HandleTypeDef hrtc;

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

void MX_RTC_Init(void);

/* USER CODE BEGIN Prototypes */
// ��ȡ��ǰRTCʱ�������
void RTC_GetDateTime(RTC_TimeTypeDef *time, RTC_DateTypeDef *date);

// ����RTCʱ�������
HAL_StatusTypeDef RTC_SetDateTime(uint8_t hour, uint8_t minute, uint8_t second,
                               uint8_t day, uint8_t month, uint8_t year);

// ����RTCʱ��
HAL_StatusTypeDef RTC_SetTime(uint8_t hour, uint8_t minute, uint8_t second);

// ����RTC����
HAL_StatusTypeDef RTC_SetDate(uint8_t day, uint8_t month, uint8_t year);

// ���ʱ���Ƿ��ڹ���ʱ�����
int RTC_IsWorkTime(RTC_TimeTypeDef *time, uint8_t startHour, uint8_t startMinute,
                  uint8_t endHour, uint8_t endMinute);

// �޸Ļ���ʱ��
void RTC_ChangeWakeUpTime(uint32_t seconds);

// �������õ����ݼĴ���
void RTC_SaveSettings(void);

// �ӱ��ݼĴ�����������
void RTC_LoadSettings(void);
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __RTC_H__ */

