# AIR780EG GPS模块使用说明

## 硬件连接
- **CAT1模块**：AIR780EG（集成GPS功能）
- **通信接口**：USART1（115200 8N1）
- **电源控制**：CAT1_PWR_Pin（GPIO控制）

## GPS数据获取方式

### 重要发现
AIR780EG的GPS数据**不会主动透传NMEA**到串口，必须通过AT指令主动查询获取。

### 初始化流程
```c
// 1. 开启CAT1模块电源
CAT1_PWR_ON;

// 2. 初始化模块（发送2遍AT唤醒+关闭回显）
gsm_init();

// 3. 开启GPS功能
gsm_send_at_command("AT+CGNSPWR=1", response, 1000);
```

### 数据查询方式
```c
// 发送查询指令
gsm_send_at_command("AT+CGNSINF", response, 3000);

// 返回格式示例：
// +CGNSINF: 1,1,20250903093120,30.276063,119.962053,31.399,0.23,0.00,3,,5.82,2.36,4.00,,14,10,,,44,,
```

## AT+CGNSINF 返回格式解析

### 字段定义（逗号分隔，共21个字段）
| 索引 | 字段名 | 示例值 | 说明 |
|------|--------|--------|------|
| 0 | run | 1 | GPS运行状态（0=停止，1=运行） |
| 1 | fix | 1 | 定位状态（0=未定位，1=已定位） |
| 2 | utc | 20250903093120 | UTC时间（yyyyMMddHHmmss） |
| 3 | lat | 30.276063 | 纬度（度，小数格式） |
| 4 | lon | 119.962053 | 经度（度，小数格式） |
| 5 | alt | 31.399 | 海拔高度（米） |
| 6 | speed | 0.23 | 速度（km/h） |
| 7 | course | 0.00 | 方位角（度） |
| 8 | fix_mode | 3 | 定位模式（1=无效，2=2D，3=3D） |
| 9 | reserved1 | (空) | 保留字段 |
| 10 | hdop | 5.82 | 水平精度因子 |
| 11 | pdop | 2.36 | 位置精度因子 |
| 12 | vdop | 4.00 | 垂直精度因子 |
| 13 | reserved2 | (空) | 保留字段 |
| 14 | gps_satellites | 14 | GPS卫星数量 |
| 15 | glonass_satellites | 10 | GLONASS卫星数量 |
| 16 | reserved3 | (空) | 保留字段 |
| 17 | reserved4 | (空) | 保留字段 |
| 18 | cn0_max | 44 | 最大信噪比（dBHz） |
| 19 | reserved5 | (空) | 保留字段 |
| 20 | reserved6 | (空) | 保留字段 |

### 解析注意事项
1. **空字段处理**：数据中包含多个空字段（连续逗号），不能使用strtok()，需手动解析
2. **字符串截断**：解析前需去除换行符和"OK"响应
3. **字段验证**：确保解析出至少16个字段才进行数据提取

## 正式应用函数接口

### 核心函数说明

#### 1. GPS_UpdateAndDisplay()
```c
GPS_Status_t GPS_UpdateAndDisplay(void);
```
- **功能**：查询GPS数据并显示详细信息（包含信号质量评估）
- **返回值**：GPS_OK=成功, GPS_ERROR=失败
- **使用场景**：测试调试、实时监控显示

#### 2. GPS_GetCurrentPosition()
```c
GPS_Status_t GPS_GetCurrentPosition(float *latitude, float *longitude, float *altitude);
```
- **功能**：获取当前GPS位置坐标
- **参数**：
  - latitude: 纬度指针（度，小数格式）
  - longitude: 经度指针（度，小数格式）
  - altitude: 海拔指针（米）
- **返回值**：GPS_OK=成功, GPS_NO_FIX=无定位, GPS_ERROR=参数错误
- **使用场景**：数据记录、位置上报、导航应用

#### 3. GPS_IsPositionValid()
```c
uint8_t GPS_IsPositionValid(void);
```
- **功能**：检查GPS位置是否有效
- **返回值**：1=有效定位, 0=无定位
- **使用场景**：条件判断、状态检查

#### 4. GPS_PrintDetailedInfo()
```c
void GPS_PrintDetailedInfo(void);
```
- **功能**：打印详细GPS信息（替代原main.c中的显示代码）
- **特点**：包含信号质量评估和警告信息
- **使用场景**：调试输出、状态显示

#### 5. GPS_EvaluateSignalQuality()
```c
void GPS_EvaluateSignalQuality(void);
```
- **功能**：评估GPS信号质量并输出警告
- **输出内容**：
  - CN0信号强度评级
  - HDOP/PDOP精度警告
  - 卫星几何分布分析
- **使用场景**：信号质量诊断

### 应用模式示例

#### 模式1：简单定期查询
```c
void main_loop() {
    static uint32_t last_time = 0;
    uint32_t current_time = HAL_GetTick();

    if ((current_time - last_time) >= 5000) {  // 每5秒
        GPS_UpdateAndDisplay();
        last_time = current_time;
    }
}
```

#### 模式2：位置数据获取
```c
void get_position_data() {
    float lat, lon, alt;

    if (GPS_QueryAIR780EG() == GPS_OK && GPS_IsPositionValid()) {
        if (GPS_GetCurrentPosition(&lat, &lon, &alt) == GPS_OK) {
            // 使用位置数据
            save_to_file(lat, lon, alt);
            send_to_server(lat, lon, alt);
        }
    }
}
```

#### 模式3：条件触发查询
```c
void conditional_gps_query() {
    static uint8_t need_position = 0;

    // 触发条件（按键、定时器、传感器等）
    if (button_pressed || timer_expired) {
        need_position = 1;
    }

    if (need_position) {
        if (GPS_UpdateAndDisplay() == GPS_OK && GPS_IsPositionValid()) {
            need_position = 0;  // 获取成功，清除标志
        }
    }
}
```

## 代码实现要点

### 字符串解析（关键）
```c
// 错误方式：strtok会跳过空字段
char *token = strtok(data, ",");  // ❌

// 正确方式：手动解析保留空字段
char *start = data;
char *pos = data;
while (*pos && token_count < 25) {
    if (*pos == ',') {
        *pos = '\0';
        tokens[token_count++] = start;
        start = pos + 1;
    }
    pos++;
}
```

### 数据提取示例
```c
if (token_count >= 16) {
    int run = atoi(tokens[0]);
    int fix = atoi(tokens[1]);
    char *utc = tokens[2];
    char *lat_str = tokens[3];
    char *lon_str = tokens[4];
    char *alt_str = tokens[5];
    char *speed_str = tokens[6];
    char *course_str = tokens[7];
    char *hdop_str = tokens[10];
    char *pdop_str = tokens[11];
    char *vdop_str = tokens[12];
    int gps_sats = atoi(tokens[14]);
    int glonass_sats = atoi(tokens[15]);
    char *cn0_max_str = tokens[18];
}
```

## 使用建议

### 查询频率
- **正常使用**：每5-10秒查询一次
- **快速定位**：每1-2秒查询一次
- **省电模式**：每30-60秒查询一次

### 错误处理
1. 检查GPS运行状态（run字段）
2. 检查定位状态（fix字段）
3. 验证关键字段非空
4. 处理AT指令超时

### 性能优化
1. 缓存上次有效定位数据
2. 根据定位状态调整查询频率
3. 在室内或信号差时降低查询频率

## 测试验证

### 成功案例
```
=== GPS Information ===
GPS Run: 1, Fix: 1
UTC: 20250903093120
Latitude: 30.276063
Longitude: 119.962053
Altitude: 31.399 m
Speed: 0.23 km/h
Course: 0.00 deg
Satellites: GPS=14, GLONASS=10, Total=24
HDOP: 5.82, PDOP: 2.36, VDOP: 4.00
CN0 Max: 44 dBHz
=======================
```

### 搜星阶段
```
GPS Run: 1, Fix: 0
No GPS fix (searching satellites...)
Satellites: GPS=11, GLONASS=0, Total=11
```

## 信号质量评估

### CN0信号强度标准
| CN0值(dBHz) | 信号质量 | 说明 |
|-------------|----------|------|
| >45 | 优秀 | 信号很强，定位稳定 |
| 40-45 | 良好 | 信号较强，定位可靠 |
| 35-40 | 一般 | 信号中等，可能不稳定 |
| 30-35 | 较弱 | 信号弱，定位困难 |
| <30 | 很弱 | 信号很弱，无法定位 |

### 精度因子(DOP)标准
| DOP值 | 精度等级 | 说明 |
|-------|----------|------|
| <2.0 | 优秀 | 高精度定位 |
| 2.0-5.0 | 良好 | 可接受精度 |
| 5.0-10.0 | 中等 | 精度较差 |
| 10.0-20.0 | 较差 | 精度很差 |
| >20.0 | 很差 | 建议更换位置 |

### 卫星数量与精度关系
- **4颗**：最少定位要求（2D定位）
- **5颗**：3D定位最少要求
- **8-12颗**：理想数量，精度稳定
- **>12颗**：数量充足，但需关注几何分布

## 调试经验总结

### 数据解析问题排查
1. **字段数量检查**：确保解析出足够字段数（>=19）
2. **空字段处理**：使用手动解析而非strtok()
3. **数据类型转换**：使用simple_atof()替代atof()避免库依赖
4. **字段索引验证**：严格按照文档索引访问字段

### GPS状态异常排查
#### 现象：有数据又没数据（Fix状态频繁切换）
**原因分析：**
- 信号质量差（HDOP>20, PDOP>10）
- 环境遮挡（室内、高楼、树木）
- 卫星几何分布不佳
- 多径效应干扰

**解决方案：**
1. **环境优化**：移动到室外开阔地带
2. **天线检查**：确保GPS天线连接良好，朝向天空
3. **等待稳定**：GPS需要时间优化卫星选择
4. **位置调整**：避开高楼、金属结构等干扰源

#### 现象：卫星数量多但精度差
**原因：**
- 卫星几何分布差（GDOP问题）
- 部分卫星信号质量差
- 多颗卫星聚集在同一方向

**判断标准：**
```c
if (satellites >= 12 && hdop > 10.0) {
    // 卫星数量足够但几何分布差
    printf("Many satellites but poor geometry\n");
}
```

### 实际测试数据分析
#### 良好信号示例
```
+CGNSINF: 1,1,20250904013414,30.276234,119.961405,225.399,0.45,0.00,3,,6.03,4.69,4.00,,11,6,,,35,,
结果：17颗卫星，HDOP=6.03，PDOP=4.69，CN0=35 dBHz
评估：卫星充足，精度可接受，信号一般
```

#### 信号突变示例
```
+CGNSINF: 1,1,20250904013455,30.276393,119.961392,224.800,1.61,0.00,3,,37.8,16.7,4.00,,11,6,,,35,,
结果：17颗卫星，HDOP=37.8，PDOP=16.7，CN0=35 dBHz
评估：卫星充足但几何分布很差，需要等待或移动位置
```

## 代码优化建议

### 信号质量监控
```c
// 添加信号质量评估
void GPS_EvaluateSignalQuality(GPS_Data_t *gps) {
    // CN0信号强度评估
    if (gps->cn0_max >= 45) {
        printf("Signal: Excellent\n");
    } else if (gps->cn0_max >= 40) {
        printf("Signal: Good\n");
    } else if (gps->cn0_max >= 35) {
        printf("Signal: Fair\n");
    } else {
        printf("Signal: Weak\n");
    }

    // 精度警告
    if (gps->hdop > 20.0) {
        printf("WARNING: Very poor accuracy - Move to open area\n");
    }

    // 几何分布分析
    if (gps->satellites >= 12 && gps->hdop > 10.0) {
        printf("INFO: Poor satellite geometry - Try different location\n");
    }
}
```

### 稳定性改进
```c
// 添加数据稳定性检查
typedef struct {
    float last_hdop;
    uint32_t stable_count;
    uint32_t unstable_count;
} GPS_Stability_t;

void GPS_CheckStability(GPS_Data_t *gps, GPS_Stability_t *stability) {
    if (fabs(gps->hdop - stability->last_hdop) < 2.0) {
        stability->stable_count++;
        stability->unstable_count = 0;
    } else {
        stability->unstable_count++;
        stability->stable_count = 0;
    }

    if (stability->stable_count >= 3) {
        printf("GPS: Signal stabilized\n");
    } else if (stability->unstable_count >= 3) {
        printf("GPS: Signal unstable - Check environment\n");
    }

    stability->last_hdop = gps->hdop;
}
```

## 常见问题

### Q: 为什么没有NMEA数据输出？
A: AIR780EG默认不透传NMEA，只能通过AT+CGNSINF查询。

### Q: 定位需要多长时间？
A: 冷启动通常需要30秒-2分钟，热启动5-30秒。

### Q: 如何提高定位精度？
A: 确保天线位置良好，等待更多卫星锁定，HDOP值越小精度越高。

### Q: 室内能定位吗？
A: 室内信号弱，建议移到窗边或室外测试。

### Q: 为什么卫星数量多但精度差？
A: 卫星数量不等于定位精度，关键是卫星几何分布。当HDOP>10且卫星>12时，说明卫星分布不佳，建议更换位置或等待。

### Q: GPS状态为什么频繁切换？
A: 通常是信号质量差导致，检查：
1. 环境是否有遮挡
2. HDOP/PDOP是否过高(>20/10)
3. CN0信号强度是否过低(<35)
4. 是否在室内或高楼密集区

### Q: 如何判断GPS信号质量？
A: 综合评估三个指标：
- **CN0**: >40为良好，35-40为一般，<35为较差
- **HDOP**: <5为良好，5-10为一般，>10为较差
- **卫星数**: >8为充足，4-8为基本，<4为不足
