/**
  ******************************************************************************
  * @file    rtc_sync.h
  * @brief   GPS时间与RTC同步机制的头文件
  ******************************************************************************
  */

#ifndef __RTC_SYNC_H
#define __RTC_SYNC_H

#ifdef __cplusplus
extern "C" {
#endif

/* 包含头文件 */
#include "main.h"
#include "rtc.h"
#include "GPS.h"

/* 宏定义 */
#define RTC_SYNC_SUCCESS       0   // 同步成功
#define RTC_SYNC_NO_GPS        1   // 无GPS数据
#define RTC_SYNC_INVALID_DATA  2   // GPS数据无效
#define RTC_SYNC_INVALID_DATE  3   // 日期无效
#define RTC_SYNC_HAL_ERROR     4   // HAL错误
#define RTC_SYNC_TIMEOUT       5   // 超时

#define RTC_SYNC_MAX_ATTEMPTS  10  // 最大尝试次数
#define RTC_SYNC_ATTEMPT_DELAY 1000 // 尝试间隔(ms)
#define RTC_SYNC_TIMEOUT_MS    30000 // 超时时间(ms)

/* 函数声明 */
/**
  * @brief  初始化RTC同步机制
  * @param  无
  * @retval 无
  */
void RTC_Sync_Init(void);

/**
  * @brief  检查RTC是否需要同步
  * @param  无
  * @retval 1: 需要同步, 0: 不需要同步
  */
uint8_t RTC_Sync_IsNeeded(void);

/**
  * @brief  尝试使用GPS数据同步RTC
  * @param  无
  * @retval 同步状态码
  */
uint8_t RTC_Sync_TrySync(void);

/**
  * @brief  等待GPS数据并同步RTC
  * @param  timeout_ms: 超时时间(ms)
  * @retval 同步状态码
  */
uint8_t RTC_Sync_WaitAndSync(uint32_t timeout_ms);

/**
  * @brief  获取上次同步时间
  * @param  time: RTC时间结构体指针
  * @param  date: RTC日期结构体指针
  * @retval 无
  */
void RTC_Sync_GetLastSyncTime(RTC_TimeTypeDef *time, RTC_DateTypeDef *date);

/**
  * @brief  获取同步状态描述
  * @param  status: 同步状态码
  * @retval 状态描述字符串
  */
const char* RTC_Sync_GetStatusString(uint8_t status);

/**
  * @brief  获取当前RTC同步状态
  * @param  无
  * @retval 当前同步状态码
  */
uint8_t RTC_Sync_GetCurrentStatus(void);

#ifdef __cplusplus
}
#endif

#endif /* __RTC_SYNC_H */
